目  录

目  录	I
摘  要	II
1、课程设计的目的及要求	1
1.1 课程设计目的	1
1.2课程设计要求	1
2、题目分析	1
2.1 功能分析	1
2.2 性能需求分析	1
2.3 通信要求分析	1
3、系统设计	2
3.1系统整体设计	2
3.2硬件设计	2
3.2.1 硬件结构设计	2
3.2.2 显示模块设计	2
3.2.3 通信模块设计	2
3.2.4 蜂鸣器模块设计	2
3.3软件设计	2
3.3.1 日历时钟程序设计	3
3.3.2 数码显示程序设计	3
3.3.3 数码显示程序设计	3
3.3.4 串口通信程序设计	3
3.3.5 通信协议设计	3
3.3.6 闹钟组程序设计	3
3.3.4 闹钟铃声程序设计	3
4 系统调试和故障处理	3
4.1 系统测试方法	3
4.2 系统调试及结果	3
4.3 所遇到的问题、故障及解决办法	3
4.4 心得体会	3
附件：程序代码	4






摘  要
（正文为宋体小四号，20磅行距，图片须有下标且小一号字体，文档完成后，点击“更新目录”，形成对应的目录表，双面打印）

关键词：（3-5个）

1、课程设计的目的及要求
（课程设计内容简介）
1.1 课程设计目的
(1)了解并熟悉常用电子元器件工作原理和功能特性；
(2)熟练运用keil软件进行单片机的C语言编程；
(3)掌握STM32的GPIO、NVIC、USART、TIME、PWM、SPI等硬件接口设计和程序设计；
(4)掌握时间日历结构体数据结构设计；
(5)掌握MAX7219接口与程序设计；
(6)掌握蜂鸣器发声设计与PWM发声程序设计；
(7)掌握串口接口设计、串口通信协议设计；
(8)掌握嵌入式设计、调试、编程工具，并运用工具完成系统设计、调试分析；
(9)培养嵌入式系统设计、分析能力；
(10)培养嵌入式设计过程中的沟通交流能力；
(11)培养查阅并利用文献指导系统设计、分析、调试的能力。
1.2课程设计要求
（说明你在课程设计中所选择的软硬件方案，详细说明其要求）
2、题目分析
将数字钟当作一个项目，对需要完成的任务进行分析，主要包括功能分析和性能分析。
【文档中蓝色部分是例子，请同学们根据自己对课程设计的理解和完成情况来写。以下同】
2.1 功能分析
......
2.2 性能需求分析
......
2.3 通信要求分析
通信格式等分析。
3、系统设计
（系统设计内容简介）
3.1系统整体设计
此部分介绍的系统组成和主要模块。至少包括以下2张图：系统硬件结构图、系统软件功能框图。

图1 数字钟硬件结构图

图2 数字钟软件功能框图图
3.2硬件设计
3.2.1 硬件结构设计
（主要画出硬件部分模块的结构图，并进行相应的描述。）
3.2.2 显示模块设计

3.2.3 通信模块设计

3.2.4 蜂鸣器模块设计

3.3软件设计
（注意：该部分侧重算法描述，最好附有一定的流程图，流程图最好用visio画，代码粘贴到附件中，展示核心代码及分析）
【下面蓝色部分是例子，请根据自己的理解和设计方案来写。】
3.3.1 日历时钟程序设计
3.3.2 数码显示程序设计
3.3.3 数码显示程序设计
3.3.4 串口通信程序设计
3.3.5 通信协议设计
（至少画一个表，说明通信协议内容，如：功能、发送内容、返回内容、校验等。）
3.3.6 闹钟组程序设计
3.3.4 闹钟铃声程序设计

4 系统调试和故障处理
（系统调试方法及故障处理内容简介）
4.1 系统测试方法

4.2 系统调试及结果

4.3 所遇到的问题、故障及解决办法
（罗列在课程设计中曾经出现的问题、故障，进行现象描述和原因分析，说明处理和解决办法。尽可能地总结经验教训。）
4.4 心得体会


附件：程序代码