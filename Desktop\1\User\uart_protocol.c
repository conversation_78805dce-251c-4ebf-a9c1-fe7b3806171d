#include "uart_protocol.h"
#include <stdio.h>
#include <string.h>
#include <stdlib.h>

// 自动发送时间全局变量
static uint8_t g_AutoTimeEnabled = 0;
static uint32_t g_LastAutoTimeSend = 0;

// 数据帧接收缓冲区
static RecvBuffer g_RecvBuffer = {RECV_IDLE, {0}, 0, 0, 0};

// 当前接收的数据帧
static UartProtocolFrame g_CurrentFrame;

// 协议初始化
void UART_Protocol_Init(void)
{
    g_AutoTimeEnabled = 0;
    g_LastAutoTimeSend = 0;
    memset(&g_RecvBuffer, 0, sizeof(g_RecvBuffer));
    g_RecvBuffer.state = RECV_IDLE;

    // 打印欢迎信息
    printf("\r\n====================================\r\n");
    printf("Clock System Dual Protocol v2.0\r\n");
    printf("Text commands & Binary frames supported\r\n");
    printf("Type 'help' for command list\r\n");
    printf("====================================\r\n");
}

// 处理接收到的串口数据（支持文本命令和数据帧两种格式）
void UART_Protocol_Process(void)
{
    uint8_t cmd;
    char *cmd_str;

    // 检查是否有数据可用
    if(USART_RX_STA & 0x8000)
    {
        // 获取接收数据长度
        uint16_t len = USART_RX_STA & 0x3FFF;

        // 检查是否为数据帧格式（以0xAA开头）
        if(len > 0 && USART_RX_BUF[0] == FRAME_HEADER)
        {
            // 数据帧格式处理
            for(uint16_t i = 0; i < len; i++)
            {
                UART_Frame_Process(USART_RX_BUF[i]);
            }
        }
        else
        {
            // 文本命令格式处理
            // 添加字符串结束符
            USART_RX_BUF[len] = 0;

            // 转换为字符串
            cmd_str = (char *)USART_RX_BUF;

            // 解析命令
            cmd = UART_Parse_Command(cmd_str);

            // 执行命令
            UART_Execute_Text_Command(cmd, cmd_str, len);
        }

        // 清除接收标志
        USART_RX_STA = 0;
    }
}

// 数据帧处理函数
void UART_Frame_Process(uint8_t byte)
{
    switch(g_RecvBuffer.state)
    {
        case RECV_IDLE:
            if(byte == FRAME_HEADER)
            {
                g_RecvBuffer.state = RECV_CMD;
                g_RecvBuffer.index = 0;
                g_CurrentFrame.header = byte;
            }
            break;

        case RECV_CMD:
            g_CurrentFrame.cmd = byte;
            g_RecvBuffer.state = RECV_LEN;
            break;

        case RECV_LEN:
            if(byte <= MAX_DATA_LEN)
            {
                g_CurrentFrame.data_len = byte;
                g_RecvBuffer.expected_len = byte;
                g_RecvBuffer.data_count = 0;
                if(byte == 0)
                {
                    g_RecvBuffer.state = RECV_CHECKSUM;
                }
                else
                {
                    g_RecvBuffer.state = RECV_DATA;
                }
            }
            else
            {
                // 数据长度无效，重置状态
                g_RecvBuffer.state = RECV_IDLE;
            }
            break;

        case RECV_DATA:
            g_CurrentFrame.data[g_RecvBuffer.data_count] = byte;
            g_RecvBuffer.data_count++;
            if(g_RecvBuffer.data_count >= g_RecvBuffer.expected_len)
            {
                g_RecvBuffer.state = RECV_CHECKSUM;
            }
            break;

        case RECV_CHECKSUM:
            g_CurrentFrame.checksum = byte;
            g_RecvBuffer.state = RECV_TAIL;
            break;

        case RECV_TAIL:
            if(byte == FRAME_TAIL)
            {
                g_CurrentFrame.tail = byte;

                // 验证校验和
                uint8_t calc_checksum = UART_Calculate_Checksum((uint8_t*)&g_CurrentFrame,
                                                               3 + g_CurrentFrame.data_len);
                if(calc_checksum == g_CurrentFrame.checksum)
                {
                    // 校验成功，处理命令
                    UART_Handle_Command(&g_CurrentFrame);
                }
                else
                {
                    // 校验失败，发送错误响应
                    UART_Send_Response(g_CurrentFrame.cmd, RESP_ERROR, NULL, 0);
                }
            }
            // 重置状态
            g_RecvBuffer.state = RECV_IDLE;
            break;

        default:
            g_RecvBuffer.state = RECV_IDLE;
            break;
    }
}

// 计算校验和
uint8_t UART_Calculate_Checksum(uint8_t *data, uint8_t len)
{
    uint8_t checksum = 0;
    for(uint8_t i = 0; i < len; i++)
    {
        checksum += data[i];
    }
    return checksum;
}

// 发送数据帧
void UART_Send_Frame(uint8_t cmd, uint8_t *data, uint8_t len)
{
    UartProtocolFrame frame;

    // 构建数据帧
    frame.header = FRAME_HEADER;
    frame.cmd = cmd;
    frame.data_len = len;

    // 复制数据
    if(data != NULL && len > 0)
    {
        memcpy(frame.data, data, len);
    }

    // 计算校验和
    frame.checksum = UART_Calculate_Checksum((uint8_t*)&frame, 3 + len);
    frame.tail = FRAME_TAIL;

    // 发送数据帧
    uint8_t *frame_bytes = (uint8_t*)&frame;
    for(uint8_t i = 0; i < (5 + len); i++)
    {
        while(USART_GetFlagStatus(USART1, USART_FLAG_TC) == RESET);
        USART_SendData(USART1, frame_bytes[i]);
    }
}

// 发送响应帧
void UART_Send_Response(uint8_t cmd, uint8_t result, uint8_t *data, uint8_t len)
{
    uint8_t response_data[MAX_DATA_LEN];
    response_data[0] = result;

    if(data != NULL && len > 0)
    {
        memcpy(&response_data[1], data, len);
    }

    UART_Send_Frame(cmd, response_data, len + 1);
}

// 命令处理分发函数
void UART_Handle_Command(UartProtocolFrame *frame)
{
    switch(frame->cmd)
    {
        case CMD_GET_TIME:
            UART_Handle_GetTime(frame);
            break;

        case CMD_SET_TIME:
            UART_Handle_SetTime(frame);
            break;

        case CMD_GET_ALARM:
            UART_Handle_GetAlarm(frame);
            break;

        case CMD_SET_ALARM:
            UART_Handle_SetAlarm(frame);
            break;

        case CMD_LIST_ALARMS:
            UART_Handle_ListAlarms(frame);
            break;

        case CMD_DELETE_ALARM:
            UART_Handle_DeleteAlarm(frame);
            break;

        case CMD_ENABLE_ALARM:
            UART_Handle_EnableAlarm(frame);
            break;

        case CMD_DISABLE_ALARM:
            UART_Handle_DisableAlarm(frame);
            break;

        case CMD_GET_STATUS:
            UART_Handle_GetStatus(frame);
            break;

        case CMD_START_AUTO_TIME:
            UART_Handle_StartAutoTime(frame);
            break;

        case CMD_STOP_AUTO_TIME:
            UART_Handle_StopAutoTime(frame);
            break;

        default:
            UART_Send_Response(frame->cmd, RESP_INVALID_CMD, NULL, 0);
            break;
    }
}

// ==================== 数据帧命令处理函数 ====================

// 处理获取时间命令
void UART_Handle_GetTime(UartProtocolFrame *frame)
{
    uint8_t time_data[8];

    // 打包时间数据：年(2字节) + 月 + 日 + 时 + 分 + 秒 + 星期
    time_data[0] = (g_DateTime.year >> 8) & 0xFF;  // 年高字节
    time_data[1] = g_DateTime.year & 0xFF;         // 年低字节
    time_data[2] = g_DateTime.month;               // 月
    time_data[3] = g_DateTime.day;                 // 日
    time_data[4] = g_DateTime.hour;                // 时
    time_data[5] = g_DateTime.minute;              // 分
    time_data[6] = g_DateTime.second;              // 秒
    time_data[7] = g_DateTime.week;                // 星期

    UART_Send_Response(CMD_GET_TIME, RESP_SUCCESS, time_data, 8);
}

// 处理设置时间命令
void UART_Handle_SetTime(UartProtocolFrame *frame)
{
    if(frame->data_len != 7)  // 需要7字节数据：年(2字节) + 月 + 日 + 时 + 分 + 秒
    {
        UART_Send_Response(CMD_SET_TIME, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    DateTime new_time;

    // 解析时间数据
    new_time.year = (frame->data[0] << 8) | frame->data[1];
    new_time.month = frame->data[2];
    new_time.day = frame->data[3];
    new_time.hour = frame->data[4];
    new_time.minute = frame->data[5];
    new_time.second = frame->data[6];
    new_time.millisecond = 0;

    // 验证时间参数
    if(new_time.year < 2000 || new_time.year > 2099 ||
       new_time.month < 1 || new_time.month > 12 ||
       new_time.day < 1 || new_time.day > 31 ||
       new_time.hour > 23 || new_time.minute > 59 || new_time.second > 59)
    {
        UART_Send_Response(CMD_SET_TIME, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    // 计算星期
    new_time.week = GetDayOfWeek(new_time.year, new_time.month, new_time.day);

    // 设置新时间
    RTC_SetTime(&new_time);

    UART_Send_Response(CMD_SET_TIME, RESP_SUCCESS, NULL, 0);
}

// 处理获取闹钟命令
void UART_Handle_GetAlarm(UartProtocolFrame *frame)
{
    if(frame->data_len != 1)  // 需要1字节索引
    {
        UART_Send_Response(CMD_GET_ALARM, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    uint8_t index = frame->data[0];

    if(index >= g_AlarmManager.count)
    {
        UART_Send_Response(CMD_GET_ALARM, RESP_INVALID_INDEX, NULL, 0);
        return;
    }

    Alarm *alarm = &g_AlarmManager.alarms[index];
    uint8_t alarm_data[24];  // 时间(3字节) + 重复模式(1字节) + 启用状态(1字节) + 名称(最多19字节)

    alarm_data[0] = alarm->hour;
    alarm_data[1] = alarm->minute;
    alarm_data[2] = alarm->second;
    alarm_data[3] = alarm->days;
    alarm_data[4] = alarm->enabled;

    // 复制闹钟名称
    uint8_t name_len = strlen(alarm->name);
    if(name_len > 19) name_len = 19;
    memcpy(&alarm_data[5], alarm->name, name_len);

    UART_Send_Response(CMD_GET_ALARM, RESP_SUCCESS, alarm_data, 5 + name_len);
}

// 处理设置闹钟命令
void UART_Handle_SetAlarm(UartProtocolFrame *frame)
{
    if(frame->data_len < 6)  // 至少需要：索引(1) + 时间(3) + 重复模式(1) + 启用状态(1)
    {
        UART_Send_Response(CMD_SET_ALARM, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    uint8_t index = frame->data[0];

    if(index >= MAX_ALARMS)
    {
        UART_Send_Response(CMD_SET_ALARM, RESP_INVALID_INDEX, NULL, 0);
        return;
    }

    Alarm new_alarm;
    new_alarm.hour = frame->data[1];
    new_alarm.minute = frame->data[2];
    new_alarm.second = frame->data[3];
    new_alarm.days = frame->data[4];
    new_alarm.enabled = frame->data[5];

    // 验证时间参数
    if(new_alarm.hour > 23 || new_alarm.minute > 59 || new_alarm.second > 59)
    {
        UART_Send_Response(CMD_SET_ALARM, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    // 设置闹钟名称
    if(frame->data_len > 6)
    {
        uint8_t name_len = frame->data_len - 6;
        if(name_len >= sizeof(new_alarm.name)) name_len = sizeof(new_alarm.name) - 1;
        memcpy(new_alarm.name, &frame->data[6], name_len);
        new_alarm.name[name_len] = 0;
    }
    else
    {
        strcpy(new_alarm.name, "Alarm");
    }

    // 设置或添加闹钟
    if(index < g_AlarmManager.count)
    {
        // 更新现有闹钟
        g_AlarmManager.alarms[index] = new_alarm;
    }
    else if(index == g_AlarmManager.count && g_AlarmManager.count < MAX_ALARMS)
    {
        // 添加新闹钟
        g_AlarmManager.alarms[g_AlarmManager.count] = new_alarm;
        g_AlarmManager.count++;
    }
    else
    {
        UART_Send_Response(CMD_SET_ALARM, RESP_INVALID_INDEX, NULL, 0);
        return;
    }

    UART_Send_Response(CMD_SET_ALARM, RESP_SUCCESS, NULL, 0);
}

// 处理列出闹钟命令
void UART_Handle_ListAlarms(UartProtocolFrame *frame)
{
    uint8_t list_data[32];
    list_data[0] = g_AlarmManager.count;  // 闹钟总数

    // 发送闹钟数量
    UART_Send_Response(CMD_LIST_ALARMS, RESP_SUCCESS, list_data, 1);

    // 逐个发送闹钟信息
    for(uint8_t i = 0; i < g_AlarmManager.count; i++)
    {
        Alarm *alarm = &g_AlarmManager.alarms[i];
        uint8_t alarm_data[25];

        alarm_data[0] = i;  // 闹钟索引
        alarm_data[1] = alarm->hour;
        alarm_data[2] = alarm->minute;
        alarm_data[3] = alarm->second;
        alarm_data[4] = alarm->days;
        alarm_data[5] = alarm->enabled;

        // 复制闹钟名称
        uint8_t name_len = strlen(alarm->name);
        if(name_len > 19) name_len = 19;
        memcpy(&alarm_data[6], alarm->name, name_len);

        UART_Send_Frame(CMD_LIST_ALARMS, alarm_data, 6 + name_len);

        // 短暂延时，避免数据冲突
        delay_ms(10);
    }
}

// 处理删除闹钟命令
void UART_Handle_DeleteAlarm(UartProtocolFrame *frame)
{
    if(frame->data_len != 1)
    {
        UART_Send_Response(CMD_DELETE_ALARM, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    uint8_t index = frame->data[0];

    if(index >= g_AlarmManager.count)
    {
        UART_Send_Response(CMD_DELETE_ALARM, RESP_INVALID_INDEX, NULL, 0);
        return;
    }

    if(Alarm_Delete(&g_AlarmManager, index))
    {
        UART_Send_Response(CMD_DELETE_ALARM, RESP_SUCCESS, NULL, 0);
    }
    else
    {
        UART_Send_Response(CMD_DELETE_ALARM, RESP_ERROR, NULL, 0);
    }
}

// 处理启用闹钟命令
void UART_Handle_EnableAlarm(UartProtocolFrame *frame)
{
    if(frame->data_len != 1)
    {
        UART_Send_Response(CMD_ENABLE_ALARM, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    uint8_t index = frame->data[0];

    if(index >= g_AlarmManager.count)
    {
        UART_Send_Response(CMD_ENABLE_ALARM, RESP_INVALID_INDEX, NULL, 0);
        return;
    }

    g_AlarmManager.alarms[index].enabled = 1;
    UART_Send_Response(CMD_ENABLE_ALARM, RESP_SUCCESS, NULL, 0);
}

// 处理禁用闹钟命令
void UART_Handle_DisableAlarm(UartProtocolFrame *frame)
{
    if(frame->data_len != 1)
    {
        UART_Send_Response(CMD_DISABLE_ALARM, RESP_INVALID_PARAM, NULL, 0);
        return;
    }

    uint8_t index = frame->data[0];

    if(index >= g_AlarmManager.count)
    {
        UART_Send_Response(CMD_DISABLE_ALARM, RESP_INVALID_INDEX, NULL, 0);
        return;
    }

    g_AlarmManager.alarms[index].enabled = 0;
    UART_Send_Response(CMD_DISABLE_ALARM, RESP_SUCCESS, NULL, 0);
}

// 处理获取状态命令
void UART_Handle_GetStatus(UartProtocolFrame *frame)
{
    uint8_t status_data[16];

    // 系统运行时间（秒）
    uint32_t uptime = g_TimerCounter / 1000;
    status_data[0] = (uptime >> 24) & 0xFF;
    status_data[1] = (uptime >> 16) & 0xFF;
    status_data[2] = (uptime >> 8) & 0xFF;
    status_data[3] = uptime & 0xFF;

    // 闹钟统计
    status_data[4] = g_AlarmManager.count;  // 总闹钟数

    uint8_t enabled_count = 0;
    for(uint8_t i = 0; i < g_AlarmManager.count; i++)
    {
        if(g_AlarmManager.alarms[i].enabled)
        {
            enabled_count++;
        }
    }
    status_data[5] = enabled_count;  // 启用的闹钟数

    // 内存使用情况
    status_data[6] = sizeof(DateTime);
    status_data[7] = sizeof(AlarmManager);

    // 自动时间传输状态
    status_data[8] = g_AutoTimeEnabled;

    UART_Send_Response(CMD_GET_STATUS, RESP_SUCCESS, status_data, 9);
}

// 处理开始自动时间传输命令
void UART_Handle_StartAutoTime(UartProtocolFrame *frame)
{
    g_AutoTimeEnabled = 1;
    g_LastAutoTimeSend = g_TimerCounter;
    UART_Send_Response(CMD_START_AUTO_TIME, RESP_SUCCESS, NULL, 0);
}

// 处理停止自动时间传输命令
void UART_Handle_StopAutoTime(UartProtocolFrame *frame)
{
    g_AutoTimeEnabled = 0;
    UART_Send_Response(CMD_STOP_AUTO_TIME, RESP_SUCCESS, NULL, 0);
}

// 兼容性函数：发送当前时间（文本格式，用于调试）
void UART_Send_Time(void)
{
    // 使用数据帧格式发送时间
    uint8_t time_data[8];
    time_data[0] = (g_DateTime.year >> 8) & 0xFF;
    time_data[1] = g_DateTime.year & 0xFF;
    time_data[2] = g_DateTime.month;
    time_data[3] = g_DateTime.day;
    time_data[4] = g_DateTime.hour;
    time_data[5] = g_DateTime.minute;
    time_data[6] = g_DateTime.second;
    time_data[7] = g_DateTime.week;

    UART_Send_Frame(CMD_GET_TIME, time_data, 8);
}

// 自动时间处理函数（需要在主循环中调用）
void UART_AutoTime_Process(void)
{
    if (g_AutoTimeEnabled) {
        // 每1000ms发送一次时间
        if ((g_TimerCounter - g_LastAutoTimeSend) >= 1000) {
            UART_Send_Time();
            g_LastAutoTimeSend = g_TimerCounter;
        }
    }
}

// ==================== 文本命令解析和处理函数 ====================

// 解析文本命令
uint8_t UART_Parse_Command(char *cmd_str)
{
    if(strncmp(cmd_str, "getTime", 7) == 0) {
        return CMD_GET_TIME;
    } else if(strncmp(cmd_str, "setTime", 7) == 0) {
        return CMD_SET_TIME;
    } else if(strncmp(cmd_str, "getAlarm", 8) == 0) {
        return CMD_GET_ALARM;
    } else if(strncmp(cmd_str, "setAlarm", 8) == 0) {
        return CMD_SET_ALARM;
    } else if(strncmp(cmd_str, "listAlarms", 10) == 0) {
        return CMD_LIST_ALARMS;
    } else if(strncmp(cmd_str, "deleteAlarm", 11) == 0) {
        return CMD_DELETE_ALARM;
    } else if(strncmp(cmd_str, "enableAlarm", 11) == 0) {
        return CMD_ENABLE_ALARM;
    } else if(strncmp(cmd_str, "disableAlarm", 12) == 0) {
        return CMD_DISABLE_ALARM;
    } else if(strncmp(cmd_str, "getStatus", 9) == 0) {
        return CMD_GET_STATUS;
    } else if(strncmp(cmd_str, "help", 4) == 0) {
        return CMD_HELP;
    } else if(strncmp(cmd_str, "startAutoTime", 13) == 0) {
        return CMD_START_AUTO_TIME;
    } else if(strncmp(cmd_str, "stopAutoTime", 12) == 0) {
        return CMD_STOP_AUTO_TIME;
    }

    return 0xFF; // 无效命令
}

// 执行文本命令
void UART_Execute_Text_Command(uint8_t cmd, char *cmd_str, uint16_t len)
{
    switch(cmd)
    {
        case CMD_GET_TIME:
            UART_Send_Time_Text();
            break;

        case CMD_SET_TIME:
            UART_Parse_Time(cmd_str);
            break;

        case CMD_GET_ALARM:
            // 解析闹钟索引
            if(len > 10) { // getAlarm命令应该更长
                uint8_t index = atoi((const char*)&cmd_str[9]);
                if(index < g_AlarmManager.count) {
                    UART_Send_Alarm_Text(index);
                } else {
                    printf("ERROR: Invalid alarm index\r\n");
                }
            } else {
                printf("ERROR: Missing alarm index\r\n");
            }
            break;

        case CMD_SET_ALARM:
            UART_Parse_Alarm(cmd_str);
            break;

        case CMD_LIST_ALARMS:
            UART_Send_AlarmList_Text();
            break;

        case CMD_DELETE_ALARM:
            if(len > 13) { // deleteAlarm命令应该更长
                uint8_t index = atoi((const char*)&cmd_str[12]);
                UART_Delete_Alarm_Text(index);
            } else {
                printf("ERROR: Missing alarm index\r\n");
            }
            break;

        case CMD_ENABLE_ALARM:
            if(len > 12) { // enableAlarm命令应该更长
                uint8_t index = atoi((const char*)&cmd_str[12]);
                UART_Enable_Alarm_Text(index);
            } else {
                printf("ERROR: Missing alarm index\r\n");
            }
            break;

        case CMD_DISABLE_ALARM:
            if(len > 13) { // disableAlarm命令应该更长
                uint8_t index = atoi((const char*)&cmd_str[13]);
                UART_Disable_Alarm_Text(index);
            } else {
                printf("ERROR: Missing alarm index\r\n");
            }
            break;

        case CMD_GET_STATUS:
            UART_Send_Status_Text();
            break;

        case CMD_HELP:
            UART_Send_Help_Text();
            break;

        case CMD_START_AUTO_TIME:
            UART_Start_AutoTime_Text();
            break;

        case CMD_STOP_AUTO_TIME:
            UART_Stop_AutoTime_Text();
            break;

        default:
            printf("ERROR: Unknown command\r\n");
            printf("Type 'help' for command list\r\n");
            break;
    }
}

// ==================== 文本命令处理函数 ====================

// 发送当前时间（文本格式）
void UART_Send_Time_Text(void)
{
    printf("TIME=%04d-%02d-%02d,%02d:%02d:%02d,W%d\r\n",
           g_DateTime.year, g_DateTime.month, g_DateTime.day,
           g_DateTime.hour, g_DateTime.minute, g_DateTime.second,
           g_DateTime.week);
}

// 解析并设置时间
void UART_Parse_Time(char *time_str)
{
    // 格式: setTime YYYY-MM-DD,HH:MM:SS
    DateTime new_time;

    // 查找数据开始位置
    char *data_start = strchr(time_str, ' ');
    if(data_start == NULL) {
        printf("ERROR: Invalid time format\r\n");
        printf("Correct format: setTime YYYY-MM-DD,HH:MM:SS\r\n");
        return;
    }

    data_start++; // 跳过空格

    // 解析年-月-日,时:分:秒
    if(sscanf(data_start, "%hu-%hhu-%hhu,%hhu:%hhu:%hhu",
              &new_time.year, &new_time.month, &new_time.day,
              &new_time.hour, &new_time.minute, &new_time.second) != 6) {
        printf("ERROR: Invalid time format\r\n");
        printf("Correct format: setTime YYYY-MM-DD,HH:MM:SS\r\n");
        return;
    }

    // 验证时间参数
    if(new_time.year < 2000 || new_time.year > 2099 ||
       new_time.month < 1 || new_time.month > 12 ||
       new_time.day < 1 || new_time.day > 31 ||
       new_time.hour > 23 || new_time.minute > 59 || new_time.second > 59) {
        printf("ERROR: Invalid time parameters\r\n");
        return;
    }

    // 计算星期
    new_time.week = GetDayOfWeek(new_time.year, new_time.month, new_time.day);
    new_time.millisecond = 0;

    // 设置新时间
    RTC_SetTime(&new_time);

    printf("SUCCESS: Time set to %04d-%02d-%02d %02d:%02d:%02d Weekday:%d\r\n",
           new_time.year, new_time.month, new_time.day,
           new_time.hour, new_time.minute, new_time.second,
           new_time.week);
}

// 发送闹钟信息（文本格式）
void UART_Send_Alarm_Text(uint8_t index)
{
    if(index >= g_AlarmManager.count) {
        printf("ERROR: Invalid alarm index\r\n");
        return;
    }

    Alarm *alarm = &g_AlarmManager.alarms[index];

    // 生成重复模式字符串
    char days_str[32] = {0};
    if(alarm->days == 0x7F) {
        strcpy(days_str, "Everyday");
    } else if(alarm->days == 0x3E) {
        strcpy(days_str, "Weekdays");
    } else if(alarm->days == 0x41) {
        strcpy(days_str, "Weekend");
    } else {
        if(alarm->days & 0x01) strcat(days_str, "Sun,");
        if(alarm->days & 0x02) strcat(days_str, "Mon,");
        if(alarm->days & 0x04) strcat(days_str, "Tue,");
        if(alarm->days & 0x08) strcat(days_str, "Wed,");
        if(alarm->days & 0x10) strcat(days_str, "Thu,");
        if(alarm->days & 0x20) strcat(days_str, "Fri,");
        if(alarm->days & 0x40) strcat(days_str, "Sat,");
        // 删除最后的逗号
        if(strlen(days_str) > 0) {
            days_str[strlen(days_str) - 1] = 0;
        }
    }

    printf("ALARM%d=%02d:%02d:%02d,%s,%s,%d\r\n",
           index, alarm->hour, alarm->minute, alarm->second,
           days_str, alarm->name, alarm->enabled);
}

// 解析并设置闹钟
void UART_Parse_Alarm(char *alarm_str)
{
    // 格式: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED
    // 示例: setAlarm 0,08:00:00,0111110,Wake Up,1

    Alarm new_alarm;
    uint8_t index;
    char days_str[20];
    char name[20];
    uint8_t enabled;

    // 查找数据开始位置
    char *data_start = strchr(alarm_str, ' ');
    if(data_start == NULL) {
        printf("ERROR: Invalid alarm format\r\n");
        printf("Correct format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
        return;
    }

    data_start++; // 跳过空格

    // 解析数据
    int fields = sscanf(data_start, "%hhu,%hhu:%hhu:%hhu,%[^,],%[^,],%hhu",
                        &index, &new_alarm.hour, &new_alarm.minute, &new_alarm.second,
                        days_str, name, &enabled);

    if(fields != 7) {
        printf("ERROR: Invalid alarm format\r\n");
        printf("Correct format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
        return;
    }

    // 验证参数
    if(new_alarm.hour > 23 || new_alarm.minute > 59 || new_alarm.second > 59) {
        printf("ERROR: Invalid alarm time parameters\r\n");
        return;
    }

    // 设置闹钟名称
    strncpy(new_alarm.name, name, sizeof(new_alarm.name) - 1);
    new_alarm.name[sizeof(new_alarm.name) - 1] = 0; // 确保字符串结束

    // 设置闹钟状态
    new_alarm.enabled = enabled ? 1 : 0;

    // 解析重复日期
    new_alarm.days = 0;
    for(int i = 0; i < strlen(days_str) && i < 7; i++) {
        if(days_str[i] == '1') {
            new_alarm.days |= (1 << i);
        }
    }

    // 设置或添加闹钟
    if(index < g_AlarmManager.count) {
        // 更新现有闹钟
        g_AlarmManager.alarms[index] = new_alarm;
        printf("SUCCESS: Alarm %d updated\r\n", index);
    } else if(index == g_AlarmManager.count && g_AlarmManager.count < MAX_ALARMS) {
        // 添加新闹钟
        g_AlarmManager.alarms[g_AlarmManager.count] = new_alarm;
        g_AlarmManager.count++;
        printf("SUCCESS: New alarm added, index=%d\r\n", g_AlarmManager.count - 1);
    } else {
        printf("ERROR: Invalid alarm index\r\n");
    }
}

// 发送闹钟列表（文本格式）
void UART_Send_AlarmList_Text(void)
{
    printf("ALARM_LIST: Total %d alarms\r\n", g_AlarmManager.count);

    if(g_AlarmManager.count == 0) {
        printf("No alarms configured.\r\n");
        return;
    }

    for(uint8_t i = 0; i < g_AlarmManager.count; i++) {
        UART_Send_Alarm_Text(i);
    }
}

// 删除指定闹钟（文本格式）
void UART_Delete_Alarm_Text(uint8_t index)
{
    if(index >= g_AlarmManager.count) {
        printf("ERROR: Invalid alarm index %d\r\n", index);
        return;
    }

    if(Alarm_Delete(&g_AlarmManager, index)) {
        printf("SUCCESS: Alarm %d deleted\r\n", index);
    } else {
        printf("ERROR: Failed to delete alarm %d\r\n", index);
    }
}

// 启用指定闹钟（文本格式）
void UART_Enable_Alarm_Text(uint8_t index)
{
    if(index >= g_AlarmManager.count) {
        printf("ERROR: Invalid alarm index %d\r\n", index);
        return;
    }

    g_AlarmManager.alarms[index].enabled = 1;
    printf("SUCCESS: Alarm %d enabled\r\n", index);
}

// 禁用指定闹钟（文本格式）
void UART_Disable_Alarm_Text(uint8_t index)
{
    if(index >= g_AlarmManager.count) {
        printf("ERROR: Invalid alarm index %d\r\n", index);
        return;
    }

    g_AlarmManager.alarms[index].enabled = 0;
    printf("SUCCESS: Alarm %d disabled\r\n", index);
}

// 发送系统状态信息（文本格式）
void UART_Send_Status_Text(void)
{
    printf("========== System Status ==========\r\n");
    printf("Current Time: %04d-%02d-%02d %02d:%02d:%02d (%s)\r\n",
           g_DateTime.year, g_DateTime.month, g_DateTime.day,
           g_DateTime.hour, g_DateTime.minute, g_DateTime.second,
           GetWeekdayName(g_DateTime.week));

    printf("System Uptime: %lu seconds\r\n", g_TimerCounter / 1000);
    printf("Total Alarms: %d/%d\r\n", g_AlarmManager.count, MAX_ALARMS);

    uint8_t enabled_count = 0;
    for(uint8_t i = 0; i < g_AlarmManager.count; i++) {
        if(g_AlarmManager.alarms[i].enabled) {
            enabled_count++;
        }
    }
    printf("Enabled Alarms: %d\r\n", enabled_count);

    printf("Memory Usage: DateTime=%d bytes, AlarmManager=%d bytes\r\n",
           sizeof(DateTime), sizeof(AlarmManager));

    printf("===================================\r\n");
}

// 开始自动发送时间（文本格式）
void UART_Start_AutoTime_Text(void)
{
    g_AutoTimeEnabled = 1;
    g_LastAutoTimeSend = g_TimerCounter;
    printf("SUCCESS: Auto time transmission started (every 1 second)\r\n");
}

// 停止自动发送时间（文本格式）
void UART_Stop_AutoTime_Text(void)
{
    g_AutoTimeEnabled = 0;
    printf("SUCCESS: Auto time transmission stopped\r\n");
}

// 发送帮助信息（文本格式）
void UART_Send_Help_Text(void)
{
    printf("\r\n========== Clock System UART Command Help ==========\r\n");
    printf("Available commands:\r\n\r\n");

    printf("1. getTime\r\n");
    printf("   - Description: Get current system time\r\n");
    printf("   - Format: getTime\r\n");
    printf("   - Example: getTime\r\n\r\n");

    printf("2. setTime YYYY-MM-DD,HH:MM:SS\r\n");
    printf("   - Description: Set system time\r\n");
    printf("   - Format: setTime YYYY-MM-DD,HH:MM:SS\r\n");
    printf("   - Parameters:\r\n");
    printf("     YYYY: Year (2000-2099)\r\n");
    printf("     MM: Month (01-12)\r\n");
    printf("     DD: Day (01-31)\r\n");
    printf("     HH: Hour (00-23)\r\n");
    printf("     MM: Minute (00-59)\r\n");
    printf("     SS: Second (00-59)\r\n");
    printf("   - Example: setTime 2024-01-15,14:30:25\r\n\r\n");

    printf("3. getAlarm INDEX\r\n");
    printf("   - Description: Get alarm by index\r\n");
    printf("   - Format: getAlarm INDEX\r\n");
    printf("   - Parameters:\r\n");
    printf("     INDEX: Alarm index (0-%d)\r\n", MAX_ALARMS - 1);
    printf("   - Example: getAlarm 0\r\n\r\n");

    printf("4. setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
    printf("   - Description: Set or create alarm\r\n");
    printf("   - Format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
    printf("   - Parameters:\r\n");
    printf("     INDEX: Alarm index (0-%d)\r\n", MAX_ALARMS - 1);
    printf("     HH:MM:SS: Alarm time\r\n");
    printf("     DAYS: Repeat days pattern, 7-bit binary (Sun-Sat)\r\n");
    printf("        Example patterns:\r\n");
    printf("        1111111 - Every day\r\n");
    printf("        0111110 - Weekdays only\r\n");
    printf("        1000001 - Weekends only\r\n");
    printf("     NAME: Alarm name (no commas)\r\n");
    printf("     ENABLED: 1=enabled, 0=disabled\r\n");
    printf("   - Examples:\r\n");
    printf("     setAlarm 0,07:00:00,0111110,Wake Up,1\r\n");
    printf("     setAlarm 1,22:30:00,1111111,Bedtime,1\r\n\r\n");

    printf("5. listAlarms\r\n");
    printf("   - Description: List all configured alarms\r\n");
    printf("   - Format: listAlarms\r\n");
    printf("   - Example: listAlarms\r\n\r\n");

    printf("6. deleteAlarm INDEX\r\n");
    printf("   - Description: Delete alarm by index\r\n");
    printf("   - Format: deleteAlarm INDEX\r\n");
    printf("   - Example: deleteAlarm 0\r\n\r\n");

    printf("7. enableAlarm INDEX\r\n");
    printf("   - Description: Enable alarm by index\r\n");
    printf("   - Format: enableAlarm INDEX\r\n");
    printf("   - Example: enableAlarm 0\r\n\r\n");

    printf("8. disableAlarm INDEX\r\n");
    printf("   - Description: Disable alarm by index\r\n");
    printf("   - Format: disableAlarm INDEX\r\n");
    printf("   - Example: disableAlarm 0\r\n\r\n");

    printf("9. getStatus\r\n");
    printf("   - Description: Get system status information\r\n");
    printf("   - Format: getStatus\r\n");
    printf("   - Example: getStatus\r\n\r\n");

    printf("10. help\r\n");
    printf("   - Description: Show this help information\r\n");
    printf("   - Format: help\r\n\r\n");

    printf("11. startAutoTime\r\n");
    printf("   - Description: Start automatic time transmission (every 1 second)\r\n");
    printf("   - Format: startAutoTime\r\n");
    printf("   - Example: startAutoTime\r\n\r\n");

    printf("12. stopAutoTime\r\n");
    printf("   - Description: Stop automatic time transmission\r\n");
    printf("   - Format: stopAutoTime\r\n");
    printf("   - Example: stopAutoTime\r\n\r\n");

    printf("Note: All commands must end with Enter key (CR+LF).\r\n");
    printf("System also supports binary data frame protocol.\r\n");
    printf("===============================================\r\n");
}



// ==================== 数据帧协议说明 ====================
/*
数据帧格式：
[帧头][命令][长度][数据][校验][帧尾]
 0xAA   1字节  1字节  N字节  1字节  0x55

示例：
获取时间: AA 01 00 01 55
设置时间: AA 02 07 E7 0C 1F 17 1E 32 09 55
         (2023年12月31日23:30:50)

响应格式：
[帧头][命令][长度][结果][数据][校验][帧尾]
结果码：00=成功，01=错误，02=无效命令，03=无效参数，04=无效索引
*/
void UART_Send_Help(void)
{
    printf("\r\n========== Clock System UART Command Help ==========\r\n");
    printf("Available commands:\r\n\r\n");
    
    printf("1. getTime\r\n");
    printf("   - Description: Get current system time\r\n");
    printf("   - Format: getTime\r\n");
    printf("   - Example: getTime\r\n\r\n");
    
    printf("2. setTime YYYY-MM-DD,HH:MM:SS\r\n");
    printf("   - Description: Set system time\r\n");
    printf("   - Format: setTime YYYY-MM-DD,HH:MM:SS\r\n");
    printf("   - Parameters:\r\n");
    printf("     YYYY: Year (2000-2099)\r\n");
    printf("     MM: Month (01-12)\r\n");
    printf("     DD: Day (01-31)\r\n");
    printf("     HH: Hour (00-23)\r\n");
    printf("     MM: Minute (00-59)\r\n");
    printf("     SS: Second (00-59)\r\n");
    printf("   - Example: setTime 2023-12-31,23:59:50\r\n\r\n");
    
    printf("3. getAlarm INDEX\r\n");
    printf("   - Description: Get alarm by index\r\n");
    printf("   - Format: getAlarm INDEX\r\n");
    printf("   - Parameters:\r\n");
    printf("     INDEX: Alarm index (0-%d)\r\n", MAX_ALARMS - 1);
    printf("   - Example: getAlarm 0\r\n\r\n");
    
    printf("4. setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
    printf("   - Description: Set or create alarm\r\n");
    printf("   - Format: setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED\r\n");
    printf("   - Parameters:\r\n");
    printf("     INDEX: Alarm index (0-%d)\r\n", MAX_ALARMS - 1);
    printf("     HH:MM:SS: Alarm time\r\n");
    printf("     DAYS: Repeat days pattern, 7-bit binary (Sun-Sat)\r\n");
    printf("        Example patterns:\r\n");
    printf("        1111111 - Every day\r\n");
    printf("        0111110 - Weekdays only\r\n");
    printf("        1000001 - Weekends only\r\n");
    printf("     NAME: Alarm name (no commas)\r\n");
    printf("     ENABLED: 1=enabled, 0=disabled\r\n");
    printf("   - Examples:\r\n");
    printf("     setAlarm 0,07:00:00,0111110,Wake Up,1\r\n");
    printf("     setAlarm 1,22:30:00,1111111,Bedtime,1\r\n\r\n");
    
    printf("5. listAlarms\r\n");
    printf("   - Description: List all configured alarms\r\n");
    printf("   - Format: listAlarms\r\n");
    printf("   - Example: listAlarms\r\n\r\n");

    printf("6. deleteAlarm INDEX\r\n");
    printf("   - Description: Delete alarm by index\r\n");
    printf("   - Format: deleteAlarm INDEX\r\n");
    printf("   - Example: deleteAlarm 0\r\n\r\n");

    printf("7. enableAlarm INDEX\r\n");
    printf("   - Description: Enable alarm by index\r\n");
    printf("   - Format: enableAlarm INDEX\r\n");
    printf("   - Example: enableAlarm 0\r\n\r\n");

    printf("8. disableAlarm INDEX\r\n");
    printf("   - Description: Disable alarm by index\r\n");
    printf("   - Format: disableAlarm INDEX\r\n");
    printf("   - Example: disableAlarm 0\r\n\r\n");

    printf("9. getStatus\r\n");
    printf("   - Description: Get system status information\r\n");
    printf("   - Format: getStatus\r\n");
    printf("   - Example: getStatus\r\n\r\n");

    printf("10. help\r\n");
    printf("   - Description: Show this help information\r\n");
    printf("   - Format: help\r\n\r\n");

    printf("11. startAutoTime\r\n");
    printf("   - Description: Start automatic time transmission (every 1 second)\r\n");
    printf("   - Format: startAutoTime\r\n");
    printf("   - Example: startAutoTime\r\n\r\n");

    printf("12. stopAutoTime\r\n");
    printf("   - Description: Stop automatic time transmission\r\n");
    printf("   - Format: stopAutoTime\r\n");
    printf("   - Example: stopAutoTime\r\n\r\n");

    printf("Note: All commands must end with Enter key (CR+LF).\r\n");
    printf("===============================================\r\n");
}











// ֹͣ�Զ�����ʱ��
void UART_Stop_AutoTime(void)
{
    g_AutoTimeEnabled = 0;
    printf("SUCCESS: Auto time transmission stopped\r\n");
}

