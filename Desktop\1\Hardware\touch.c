#include "touch.h"
#include "lcd.h"
#include "delay.h"
#include <stdio.h>

// 全局变量定义
TouchCalibration g_TouchCal = {
    .x_min = 200,
    .x_max = 3900,
    .y_min = 200,
    .y_max = 3900,
    .lcd_width = 800,
    .lcd_height = 480
};

TouchPoint g_TouchPoint = {0};

// 触摸屏初始化
void Touch_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    
    // 使能时钟
    RCC_APB2PeriphClockCmd(TOUCH_CS_CLK | TOUCH_IRQ_CLK, ENABLE);
    
    // 配置CS引脚 (推挽输出)
    GPIO_InitStructure.GPIO_Pin = TOUCH_CS_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(TOUCH_CS_PORT, &GPIO_InitStructure);
    
    // 配置IRQ引脚 (上拉输入)
    GPIO_InitStructure.GPIO_Pin = TOUCH_IRQ_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_Init(TOUCH_IRQ_PORT, &GPIO_InitStructure);
    
    // 初始化SPI
    Touch_SPI_Init();
    
    // CS拉高
    TOUCH_CS_HIGH();
    
    printf("Touch screen initialized\r\n");
}

// SPI初始化
void Touch_SPI_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    SPI_InitTypeDef SPI_InitStructure;
    
    // 使能SPI时钟
    RCC_APB2PeriphClockCmd(TOUCH_SPI_CLK, ENABLE);
    
    // 配置SPI引脚
    GPIO_InitStructure.GPIO_Pin = TOUCH_SCK_PIN | TOUCH_MOSI_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(TOUCH_SCK_PORT, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Pin = TOUCH_MISO_PIN;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(TOUCH_MISO_PORT, &GPIO_InitStructure);
    
    // 配置SPI
    SPI_InitStructure.SPI_Direction = SPI_Direction_2Lines_FullDuplex;
    SPI_InitStructure.SPI_Mode = SPI_Mode_Master;
    SPI_InitStructure.SPI_DataSize = SPI_DataSize_8b;
    SPI_InitStructure.SPI_CPOL = SPI_CPOL_High;
    SPI_InitStructure.SPI_CPHA = SPI_CPHA_2Edge;
    SPI_InitStructure.SPI_NSS = SPI_NSS_Soft;
    SPI_InitStructure.SPI_BaudRatePrescaler = SPI_BaudRatePrescaler_256;
    SPI_InitStructure.SPI_FirstBit = SPI_FirstBit_MSB;
    SPI_InitStructure.SPI_CRCPolynomial = 7;
    
    SPI_Init(TOUCH_SPI, &SPI_InitStructure);
    SPI_Cmd(TOUCH_SPI, ENABLE);
}

// SPI读写函数
uint8_t Touch_SPI_ReadWrite(uint8_t data)
{
    while (SPI_I2S_GetFlagStatus(TOUCH_SPI, SPI_I2S_FLAG_TXE) == RESET);
    SPI_I2S_SendData(TOUCH_SPI, data);
    
    while (SPI_I2S_GetFlagStatus(TOUCH_SPI, SPI_I2S_FLAG_RXNE) == RESET);
    return SPI_I2S_ReceiveData(TOUCH_SPI);
}

// 读取触摸屏AD值
uint16_t Touch_ReadAD(uint8_t cmd)
{
    uint16_t result = 0;
    
    TOUCH_CS_LOW();
    delay_us(1);
    
    Touch_SPI_ReadWrite(cmd);
    delay_us(6);
    
    result = Touch_SPI_ReadWrite(0x00);
    result <<= 8;
    result |= Touch_SPI_ReadWrite(0x00);
    result >>= 3;
    
    TOUCH_CS_HIGH();
    
    return result;
}

// 获取触摸点坐标
uint8_t Touch_GetPoint(TouchPoint* point)
{
    uint16_t x_raw, y_raw;
    uint16_t x_samples[5], y_samples[5];
    uint16_t x_sum = 0, y_sum = 0;
    uint8_t i;
    
    // 检查是否有触摸
    if (TOUCH_IRQ_READ() != 0) {
        point->pressed = 0;
        return 0;
    }
    
    // 连续采样5次
    for (i = 0; i < 5; i++) {
        x_samples[i] = Touch_ReadAD(TOUCH_CMD_X);
        y_samples[i] = Touch_ReadAD(TOUCH_CMD_Y);
        delay_ms(1);
    }
    
    // 计算平均值
    for (i = 0; i < 5; i++) {
        x_sum += x_samples[i];
        y_sum += y_samples[i];
    }
    
    x_raw = x_sum / 5;
    y_raw = y_sum / 5;
    
    // 坐标转换
    if (x_raw > g_TouchCal.x_min && x_raw < g_TouchCal.x_max &&
        y_raw > g_TouchCal.y_min && y_raw < g_TouchCal.y_max) {
        
        point->x = (x_raw - g_TouchCal.x_min) * g_TouchCal.lcd_width / 
                   (g_TouchCal.x_max - g_TouchCal.x_min);
        point->y = (y_raw - g_TouchCal.y_min) * g_TouchCal.lcd_height / 
                   (g_TouchCal.y_max - g_TouchCal.y_min);
        point->pressed = 1;
        
        return 1;
    }
    
    point->pressed = 0;
    return 0;
}

// 触摸扫描
uint8_t Touch_Scan(TouchPoint* point)
{
    static uint8_t last_pressed = 0;
    uint8_t current_pressed = 0;
    
    if (Touch_GetPoint(point)) {
        current_pressed = 1;
        
        // 只在按下瞬间返回1，避免重复触发
        if (!last_pressed) {
            last_pressed = 1;
            return 1;
        }
    } else {
        last_pressed = 0;
    }
    
    return 0;
}

// 设置校准参数
void Touch_SetCalibration(uint16_t x_min, uint16_t x_max, uint16_t y_min, uint16_t y_max)
{
    g_TouchCal.x_min = x_min;
    g_TouchCal.x_max = x_max;
    g_TouchCal.y_min = y_min;
    g_TouchCal.y_max = y_max;
}

// 绘制校准十字
void Touch_DrawCross(uint16_t x, uint16_t y, uint16_t color)
{
    POINT_COLOR = color;
    LCD_DrawLine(x - 10, y, x + 10, y);
    LCD_DrawLine(x, y - 10, x, y + 10);
    LCD_Draw_Circle(x, y, 5);
}

// 触摸屏校准
void Touch_Calibrate(void)
{
    TouchPoint point;
    uint16_t cal_points[4][2];
    uint8_t step = 0;
    
    LCD_Clear(WHITE);
    POINT_COLOR = RED;
    BACK_COLOR = WHITE;
    
    LCD_ShowString(200, 100, 400, 24, 24, (u8*)"Touch Calibration");
    LCD_ShowString(150, 150, 500, 16, 16, (u8*)"Please touch the cross accurately");
    
    // 校准四个点
    uint16_t cal_x[] = {50, 750, 750, 50};
    uint16_t cal_y[] = {50, 50, 430, 430};
    
    while (step < 4) {
        Touch_DrawCross(cal_x[step], cal_y[step], RED);
        
        while (1) {
            if (Touch_GetPoint(&point) && point.pressed) {
                cal_points[step][0] = Touch_ReadAD(TOUCH_CMD_X);
                cal_points[step][1] = Touch_ReadAD(TOUCH_CMD_Y);
                
                Touch_DrawCross(cal_x[step], cal_y[step], GREEN);
                delay_ms(500);
                break;
            }
            delay_ms(10);
        }
        step++;
    }
    
    // 计算校准参数 (简化处理)
    g_TouchCal.x_min = (cal_points[0][0] + cal_points[3][0]) / 2;
    g_TouchCal.x_max = (cal_points[1][0] + cal_points[2][0]) / 2;
    g_TouchCal.y_min = (cal_points[0][1] + cal_points[1][1]) / 2;
    g_TouchCal.y_max = (cal_points[2][1] + cal_points[3][1]) / 2;
    
    LCD_Clear(WHITE);
    LCD_ShowString(300, 200, 200, 24, 24, (u8*)"Calibration Done!");
    delay_ms(2000);
}
