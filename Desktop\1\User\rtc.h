#ifndef __RTC_H
#define __RTC_H

#include "stm32f10x.h"
#include <stdio.h>

// Constant definitions
#define MAX_ALARMS  10  // Maximum number of alarms

// Date and time structure
typedef struct {
    uint16_t year;   // Year (2000-2099)
    uint8_t month;   // Month (1-12)
    uint8_t day;     // Day (1-31)
    uint8_t week;    // Weekday (0-6, 0 for Sunday)
    uint8_t hour;    // Hour (0-23)
    uint8_t minute;  // Minute (0-59)
    uint8_t second;  // Second (0-59)
    uint16_t millisecond; // Millisecond (0-999)
} DateTime;

// Alarm structure
typedef struct {
    uint8_t hour;    // Hour (0-23)
    uint8_t minute;  // Minute (0-59)
    uint8_t second;  // Second (0-59)
    uint8_t enabled; // Enable status (0-disabled, 1-enabled)
    uint8_t days;    // Repeat days (bit0-bit6 for Sunday to Saturday)
    char name[20];   // Alarm name
} Alarm;

// Alarm manager structure
typedef struct {
    Alarm alarms[10];  // Maximum 10 alarms
    uint8_t count;     // Current alarm count
} AlarmManager;

// Function declarations
void RTC_Init(void);
void RTC_SetTime(DateTime* time);
void RTC_GetTime(DateTime* time);
void DateTime_Update(DateTime* time);
uint8_t IsLeapYear(uint16_t year);
void Alarm_Init(AlarmManager* manager);
uint8_t Alarm_Add(AlarmManager* manager, Alarm* alarm);
uint8_t Alarm_Delete(AlarmManager* manager, uint8_t index);
uint8_t Alarm_Update(AlarmManager* manager, uint8_t index, Alarm* alarm);
uint8_t Alarm_Check(AlarmManager* manager, DateTime* time);

// Global variable declarations
extern DateTime g_DateTime;
extern AlarmManager g_AlarmManager;
extern volatile uint32_t g_TimerCounter; // Millisecond counter
extern const uint8_t g_MonthDays[]; // Days per month array

#endif /* __RTC_H */ 
