#ifndef __KEY_H
#define __KEY_H

#include "stm32f10x.h"
#include "sys.h"

// Key definitions
#define KEY1_PIN     GPIO_Pin_15  // PA15 - Page switch (pull-up input)
#define KEY1_PORT    GPIOA
#define KEY1_CLK     RCC_APB2Periph_GPIOA

#define KEY2_PIN     GPIO_Pin_5   // PC5 - Confirm (pull-up input)
#define KEY2_PORT    GPIOC
#define KEY2_CLK     RCC_APB2Periph_GPIOC

#define KEY3_PIN     GPIO_Pin_0   // PA0 - Select (pull-down input)
#define KEY3_PORT    GPIOA
#define KEY3_CLK     RCC_APB2Periph_GPIOA

// Key state definitions (for KEY1 and KEY2, pull-up input)
#define KEY1_PRESSED  0  // Pressed is low level (pull-up input)
#define KEY1_RELEASED 1  // Released is high level

// Key state definitions (for KEY3, pull-down input)
#define KEY3_PRESSED  1  // Pressed is high level (pull-down input)
#define KEY3_RELEASED 0  // Released is low level

// Key value definitions
#define KEY_NONE     0
#define KEY1_PRESSED 1  // Page switch
#define KEY2_PRESSED 2  // Confirm
#define KEY3_PRESSED 3  // Select

// Function declarations
void KEY_Init(void);
uint8_t KEY_Scan(uint8_t mode);

#endif /* __KEY_H */ 