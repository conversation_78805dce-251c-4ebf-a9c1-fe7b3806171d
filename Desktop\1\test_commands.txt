# 嵌入式时钟系统 - 串口测试命令

## 基础功能测试

# 1. 获取帮助信息
help

# 2. 获取当前时间
getTime

# 3. 设置系统时间 (示例: 2023年12月31日 23:59:50)
setTime 2023-12-31,23:59:50

# 4. 再次获取时间确认设置成功
getTime

## 闹钟管理测试

# 5. 查看当前闹钟列表
listAlarms

# 6. 添加工作日早晨闹钟 (周一到周五 7:00:00)
setAlarm 0,07:00:00,0111110,Wake Up,1

# 7. 添加每日晚上闹钟 (每天 22:30:00)
setAlarm 1,22:30:00,1111111,Bedtime,1

# 8. 添加周末闹钟 (周六周日 09:00:00)
setAlarm 2,09:00:00,1000001,Weekend Sleep,1

# 9. 添加测试闹钟 (当前时间+10秒，用于测试触发)
# 注意：需要根据当前时间调整
setAlarm 3,00:01:00,1111111,Test Alarm,1

# 10. 查看所有闹钟
listAlarms

# 11. 获取特定闹钟信息
getAlarm 0
getAlarm 1

# 12. 禁用闹钟
disableAlarm 2

# 13. 启用闹钟
enableAlarm 2

# 14. 删除闹钟
deleteAlarm 3

# 15. 再次查看闹钟列表确认删除
listAlarms

## 系统状态测试

# 16. 获取系统状态
getStatus

## 错误处理测试

# 17. 测试无效命令
invalidCommand

# 18. 测试无效时间格式
setTime invalid-format

# 19. 测试无效闹钟索引
getAlarm 99

# 20. 测试无效闹钟设置
setAlarm 0,25:00:00,1111111,Invalid Time,1

## 使用说明

1. 将以上命令逐个复制到串口助手中发送
2. 观察系统返回的响应信息
3. 验证时钟显示是否正确更新
4. 测试闹钟触发功能（设置一个几秒后的闹钟）

## 预期结果

- 所有有效命令应该返回 "SUCCESS:" 开头的确认信息
- 无效命令应该返回 "ERROR:" 开头的错误信息
- 时间设置后，LCD显示应该立即更新
- 闹钟触发时，应该在串口输出提醒信息并LED闪烁

## 重复日期格式说明

DAYS参数是7位二进制字符串，每位代表一周中的一天：
- 位0 (最右): 周日
- 位1: 周一
- 位2: 周二
- 位3: 周三
- 位4: 周四
- 位5: 周五
- 位6 (最左): 周六

常用模式：
- 1111111 - 每天
- 0111110 - 工作日 (周一到周五)
- 1000001 - 周末 (周六周日)
- 0000001 - 仅周日
- 0100000 - 仅周五

## 注意事项

1. 所有命令必须以回车结束
2. 时间格式必须严格按照 YYYY-MM-DD,HH:MM:SS
3. 闹钟索引从0开始
4. 系统最多支持10个闹钟 (索引0-9)
5. 闹钟名称中不能包含逗号
