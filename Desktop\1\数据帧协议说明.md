# 串口数据帧通信协议说明

## 概述

本文档描述了基于STM32F103的嵌入式时钟系统的串口数据帧通信协议。系统已从原来的文本命令格式升级为二进制数据帧格式，提供更高的传输效率和可靠性。

## 数据帧格式

### 基本帧结构
```
[帧头] [命令] [长度] [数据] [校验] [帧尾]
 0xAA   1字节  1字节  N字节  1字节  0x55
```

### 字段说明
- **帧头 (0xAA)**：固定值，用于帧同步
- **命令**：1字节命令码，定义具体操作
- **长度**：1字节数据长度，表示数据字段的字节数 (0-32)
- **数据**：可变长度数据，最大32字节
- **校验**：1字节校验和，为帧头到数据的所有字节之和
- **帧尾 (0x55)**：固定值，用于帧结束标识

### 响应帧格式
```
[帧头] [命令] [长度] [结果] [数据] [校验] [帧尾]
 0xAA   1字节  1字节  1字节  N字节  1字节  0x55
```

响应帧在数据字段前增加了1字节结果码：
- **0x00**: 成功
- **0x01**: 错误
- **0x02**: 无效命令
- **0x03**: 无效参数
- **0x04**: 无效索引

## 命令定义

| 命令码 | 命令名称 | 功能描述 |
|--------|----------|----------|
| 0x01 | CMD_GET_TIME | 获取当前时间 |
| 0x02 | CMD_SET_TIME | 设置系统时间 |
| 0x03 | CMD_GET_ALARM | 获取指定闹钟 |
| 0x04 | CMD_SET_ALARM | 设置闹钟 |
| 0x05 | CMD_HELP | 获取帮助信息 |
| 0x06 | CMD_LIST_ALARMS | 列出所有闹钟 |
| 0x07 | CMD_DELETE_ALARM | 删除指定闹钟 |
| 0x08 | CMD_ENABLE_ALARM | 启用指定闹钟 |
| 0x09 | CMD_DISABLE_ALARM | 禁用指定闹钟 |
| 0x0A | CMD_GET_STATUS | 获取系统状态 |
| 0x0B | CMD_START_AUTO_TIME | 开始自动时间传输 |
| 0x0C | CMD_STOP_AUTO_TIME | 停止自动时间传输 |

## 详细命令说明

### 1. 获取时间 (0x01)

**请求帧：**
```
AA 01 00 01 55
```

**响应帧：**
```
AA 01 09 00 [年高] [年低] [月] [日] [时] [分] [秒] [星期] [校验] 55
```

**数据格式：**
- 年：2字节，高字节在前 (2000-2099)
- 月：1字节 (1-12)
- 日：1字节 (1-31)
- 时：1字节 (0-23)
- 分：1字节 (0-59)
- 秒：1字节 (0-59)
- 星期：1字节 (0-6, 0=周日)

**示例：**
```
请求: AA 01 00 01 55
响应: AA 01 09 00 07 E7 0C 1F 17 1E 32 01 XX 55
     (2023年12月31日23:30:50 周一)
```

### 2. 设置时间 (0x02)

**请求帧：**
```
AA 02 07 [年高] [年低] [月] [日] [时] [分] [秒] [校验] 55
```

**响应帧：**
```
AA 02 01 00 [校验] 55  (成功)
AA 02 01 03 [校验] 55  (参数无效)
```

**示例：**
```
请求: AA 02 07 07 E7 0C 1F 17 1E 32 XX 55
     (设置为2023年12月31日23:30:50)
响应: AA 02 01 00 XX 55 (成功)
```

### 3. 获取闹钟 (0x03)

**请求帧：**
```
AA 03 01 [索引] [校验] 55
```

**响应帧：**
```
AA 03 [长度] 00 [时] [分] [秒] [重复] [启用] [名称...] [校验] 55
```

**数据格式：**
- 索引：1字节 (0-9)
- 时：1字节 (0-23)
- 分：1字节 (0-59)
- 秒：1字节 (0-59)
- 重复：1字节位掩码 (bit0=周日, bit1=周一, ..., bit6=周六)
- 启用：1字节 (0=禁用, 1=启用)
- 名称：可变长度字符串

**示例：**
```
请求: AA 03 01 00 XX 55 (获取闹钟0)
响应: AA 03 0C 00 07 00 00 3E 01 57 61 6B 65 20 55 70 XX 55
     (07:00:00, 工作日, 启用, "Wake Up")
```

### 4. 设置闹钟 (0x04)

**请求帧：**
```
AA 04 [长度] [索引] [时] [分] [秒] [重复] [启用] [名称...] [校验] 55
```

**响应帧：**
```
AA 04 01 00 [校验] 55  (成功)
AA 04 01 03 [校验] 55  (参数无效)
AA 04 01 04 [校验] 55  (索引无效)
```

**示例：**
```
请求: AA 04 0C 00 07 00 00 3E 01 57 61 6B 65 20 55 70 XX 55
     (设置闹钟0: 07:00:00, 工作日, 启用, "Wake Up")
响应: AA 04 01 00 XX 55 (成功)
```

### 5. 列出闹钟 (0x06)

**请求帧：**
```
AA 06 00 06 55
```

**响应帧：**
```
AA 06 01 00 [数量] [校验] 55
```
然后为每个闹钟发送一个数据帧：
```
AA 06 [长度] [索引] [时] [分] [秒] [重复] [启用] [名称...] [校验] 55
```

### 6. 删除闹钟 (0x07)

**请求帧：**
```
AA 07 01 [索引] [校验] 55
```

**响应帧：**
```
AA 07 01 00 [校验] 55  (成功)
AA 07 01 04 [校验] 55  (索引无效)
```

### 7. 启用/禁用闹钟 (0x08/0x09)

**请求帧：**
```
AA 08 01 [索引] [校验] 55  (启用)
AA 09 01 [索引] [校验] 55  (禁用)
```

**响应帧：**
```
AA 08 01 00 [校验] 55  (启用成功)
AA 09 01 00 [校验] 55  (禁用成功)
```

### 8. 获取系统状态 (0x0A)

**请求帧：**
```
AA 0A 00 0A 55
```

**响应帧：**
```
AA 0A 09 00 [运行时间4字节] [总闹钟数] [启用闹钟数] [DateTime大小] [AlarmManager大小] [自动传输状态] [校验] 55
```

### 9. 自动时间传输控制 (0x0B/0x0C)

**请求帧：**
```
AA 0B 00 0B 55  (开始自动传输)
AA 0C 00 0C 55  (停止自动传输)
```

**响应帧：**
```
AA 0B 01 00 [校验] 55  (开始成功)
AA 0C 01 00 [校验] 55  (停止成功)
```

## 校验和计算

校验和为帧头到数据字段所有字节的简单累加和：
```c
uint8_t checksum = 0;
checksum += header;    // 0xAA
checksum += cmd;       // 命令码
checksum += length;    // 数据长度
for(int i = 0; i < length; i++) {
    checksum += data[i];
}
```

## 错误处理

1. **帧格式错误**：如果接收到的帧格式不正确（帧头、帧尾错误），直接丢弃
2. **校验错误**：如果校验和不匹配，发送错误响应
3. **参数错误**：如果命令参数无效，返回相应错误码
4. **超时处理**：如果接收帧不完整，设置超时重置接收状态

## 实现特点

1. **状态机接收**：使用状态机方式逐字节解析数据帧
2. **缓冲管理**：合理的接收缓冲区管理，避免数据丢失
3. **实时响应**：命令处理和响应时间控制在10ms以内
4. **向后兼容**：保留部分文本输出用于调试和兼容性

## 使用示例

### C语言示例代码

```c
// 发送获取时间命令
uint8_t get_time_cmd[] = {0xAA, 0x01, 0x00, 0x01, 0x55};
send_data(get_time_cmd, 5);

// 设置时间为2023-12-31 23:30:50
uint8_t set_time_cmd[] = {
    0xAA, 0x02, 0x07,           // 帧头、命令、长度
    0x07, 0xE7,                 // 年份2023
    0x0C, 0x1F,                 // 12月31日
    0x17, 0x1E, 0x32,           // 23:30:50
    0x00,                       // 校验和(需计算)
    0x55                        // 帧尾
};
// 计算校验和
uint8_t checksum = 0xAA + 0x02 + 0x07 + 0x07 + 0xE7 + 0x0C + 0x1F + 0x17 + 0x1E + 0x32;
set_time_cmd[10] = checksum;
send_data(set_time_cmd, 12);
```

## 总结

新的数据帧协议相比原来的文本协议具有以下优势：

1. **传输效率高**：二进制格式减少了数据量
2. **解析速度快**：无需字符串解析，直接处理二进制数据
3. **可靠性强**：包含校验和和完整的错误处理
4. **扩展性好**：易于添加新命令和数据格式
5. **实时性好**：处理速度快，响应时间短

该协议适用于需要高效、可靠串口通信的嵌入式应用场景。
