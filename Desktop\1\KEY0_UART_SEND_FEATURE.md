# KEY0串口发送时间功能说明

## 功能概述

新增了按下KEY0（对应物理按键KEY2和屏幕按钮2）后，将当前实时时间通过串口发送到PC的功能。

## 按键映射

### 物理按键
- **KEY1 (PA15)**: 闹钟管理功能
- **KEY2 (PC5)**: **KEY0功能 - 发送时间到串口**
- **KEY3 (PA0)**: WK_UP时间调整功能

### 屏幕按钮
- **按钮1 (ALARM)**: 对应KEY1功能
- **按钮2 (SEND)**: **对应KEY0功能 - 发送时间到串口**
- **按钮3 (TIME)**: 对应KEY3功能

## 功能实现

### 1. 物理按键KEY2处理

在`clock_display.c`的`Clock_ProcessKey()`函数中：

```c
case KEY2_PRESSED:  // PC5 - KEY0功能：发送时间到串口/后退/递减按钮
    switch(g_CurrentPage) {
        case PAGE_CLOCK:
            // 在时钟页面，KEY2(KEY0)发送当前时间到串口
            printf("KEY0按下，发送时间到串口\r\n");
            UART_Send_Time();
            break;
        // ... 其他页面的处理逻辑保持不变
    }
```

### 2. 屏幕按钮处理

在`clock_display.c`的`Clock_ProcessButtonPress()`函数中：

```c
case 2: // 模拟KEY0按键 - 发送时间到串口/确认/选择
    switch(g_CurrentPage) {
        case PAGE_CLOCK:
            // 在时钟页面，KEY0按钮发送当前时间到串口
            printf("屏幕KEY0按钮按下，发送时间到串口\r\n");
            UART_Send_Time();
            break;
        // ... 其他页面的处理逻辑保持不变
    }
```

### 3. 调试输出增强

在`main.c`中增强了按键调试输出：

```c
switch(key) {
    case KEY1_PRESSED:
        printf("KEY1按下: 闹钟管理\r\n");
        break;
    case KEY2_PRESSED:
        printf("KEY2(KEY0)按下: 发送时间到串口\r\n");
        break;
    case KEY3_PRESSED:
        printf("KEY3(WK_UP)按下: 时间调整\r\n");
        break;
}
```

## 串口输出格式

当按下KEY0时，系统会通过串口发送当前时间，格式如下：

```
TIME=2024-01-15,14:30:25,W1
```

格式说明：
- `TIME=` - 时间数据标识符
- `YYYY-MM-DD` - 年-月-日
- `HH:MM:SS` - 时-分-秒
- `WN` - 星期（0=周日，1=周一，...，6=周六）

## 使用场景

### 1. 手动时间同步
用户可以通过按下KEY0，主动将MCU的当前时间发送到PC端，用于：
- 时间校验
- 数据记录
- 系统同步

### 2. 调试和监控
在开发和调试过程中，可以随时查看MCU的当前时间状态。

### 3. 数据采集
PC端程序可以通过监听串口，接收MCU主动发送的时间数据。

## 操作方法

### 方法1：物理按键
1. 确保系统处于时钟显示页面（主页面）
2. 按下物理按键KEY2（PC5引脚）
3. 观察串口输出时间信息

### 方法2：屏幕按钮
1. 确保系统处于时钟显示页面（主页面）
2. 点击屏幕上的"SEND"按钮（绿色按钮）
3. 观察串口输出时间信息

## 技术细节

### 1. 头文件包含
在`clock_display.c`中添加了：
```c
#include "uart_protocol.h"
```

### 2. 函数调用
直接调用现有的`UART_Send_Time()`函数，该函数已在串口协议模块中实现。

### 3. 页面限制
KEY0的发送时间功能仅在时钟主页面（PAGE_CLOCK）生效，在其他页面（闹钟列表、闹钟编辑、时间设置）保持原有的功能逻辑。

## 兼容性

### 1. 向后兼容
- 原有的串口命令`getTime`仍然有效
- 自动时间发送功能（`startAutoTime`/`stopAutoTime`）不受影响
- 其他页面的KEY2功能保持不变

### 2. 功能互补
- 手动发送（KEY0）：用户主动触发
- 命令发送（getTime）：PC端主动请求
- 自动发送（startAutoTime）：定时自动发送

## 测试验证

### 1. 功能测试
```bash
# 在串口终端中观察输出
1. 按下KEY0
2. 应该看到类似输出：
   KEY2(KEY0)按下: 发送时间到串口
   TIME=2024-01-15,14:30:25,W1
```

### 2. PC端接收测试
```c
// PC端可以使用现有的串口客户端程序接收数据
// 或者简单的串口监听程序
```

## 总结

通过这次功能增强：

1. ✅ **新增功能**：KEY0按键可以主动发送时间到串口
2. ✅ **保持兼容**：原有功能和接口不受影响
3. ✅ **用户友好**：提供了物理按键和屏幕按钮两种操作方式
4. ✅ **调试增强**：改进了按键调试输出信息
5. ✅ **界面更新**：屏幕按钮标签更新为"SEND"，更直观地表示功能

现在用户可以通过简单的按键操作，随时将MCU的当前时间发送到PC端，为时间同步和数据监控提供了便利的手动触发方式。
