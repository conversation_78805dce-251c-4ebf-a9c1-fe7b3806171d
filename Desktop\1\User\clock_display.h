#ifndef __CLOCK_DISPLAY_H
#define __CLOCK_DISPLAY_H

#include "stm32f10x.h"
#include "lcd.h"
#include "rtc.h"
#include <math.h>

// Page definitions
#define PAGE_CLOCK       0   // Clock page
#define PAGE_ALARM_LIST  1   // Alarm list page
#define PAGE_ALARM_EDIT  2   // Alarm edit page
#define PAGE_TIME_SET    3   // Time setting page

// Analog clock parameters - readjusted to avoid conflicts
#define CLOCK_CENTER_X   120       // Clock center X coordinate (moved left to avoid right boundary)
#define CLOCK_CENTER_Y   150       // Clock center Y coordinate (moved down to avoid date conflict)
#define CLOCK_RADIUS     60        // Clock radius (moderate size to ensure full display)
#define HOUR_HAND_LEN    35        // Hour hand length (short and thick)
#define MINUTE_HAND_LEN  45        // Minute hand length (medium)
#define SECOND_HAND_LEN  55        // Second hand length (longest)

// Clock face decoration parameters
#define HOUR_MARK_LEN    8         // Hour mark length
#define MINUTE_MARK_LEN  4         // Minute mark length
#define CENTER_DOT_RADIUS 3        // Center dot radius

// Digital display positions - relayout
#define DATE_DISPLAY_X   120       // Date display X coordinate (centered)
#define DATE_DISPLAY_Y   20        // Date display Y coordinate (above clock face with enough space)
#define DIGITAL_TIME_X   120       // Digital time X coordinate (centered)
#define DIGITAL_TIME_Y   230       // Digital time Y coordinate (below clock face)

// Screen button parameters
#define BUTTON_WIDTH     60        // Button width
#define BUTTON_HEIGHT    30        // Button height
#define BUTTON_Y         270       // Button Y coordinate (below digital time)

#define BUTTON1_X        30        // Button 1 X coordinate (WK_UP function)
#define BUTTON2_X        100       // Button 2 X coordinate (KEY0 function)
#define BUTTON3_X        170       // Button 3 X coordinate (KEY1 function)

// 函数声明
void Clock_Init(void);
void Clock_Display(DateTime* time);
void Clock_DrawDigitalTime(DateTime* time);
void Clock_DrawDate(DateTime* time);
void Clock_DrawDial(void);
void Clock_DrawHands(DateTime* time);
void Clock_DrawAlarmList(void);
void Clock_DrawAlarmEdit(uint8_t index);
void Clock_DrawTimeSet(DateTime* time);
void Clock_ChangePage(uint8_t page);
void Clock_ProcessKey(uint8_t key);
uint8_t GetDayOfWeek(uint16_t year, uint8_t month, uint8_t day);

// Additional analog clock drawing functions
void Clock_DrawAnalogClock(DateTime* time);
void Clock_DrawClockFace(void);
void Clock_DrawHourMarks(void);
void Clock_DrawMinuteMarks(void);
void Clock_DrawNumbers(void);
void Clock_DrawHand(int16_t angle, uint8_t length, uint16_t color, uint8_t thickness);
void Clock_ClearHands(DateTime* last_time);
void Clock_DrawCenterDot(void);
void Clock_DrawDateInfo(DateTime* time);
int16_t Clock_CalculateAngle(uint8_t value, uint8_t max_value);

// Helper functions
void DrawThickLine(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t color, uint8_t thickness);
const char* GetWeekdayName(uint8_t weekday);
const char* GetMonthName(uint8_t month);

// Screen button related functions
void Clock_DrawScreenButtons(void);
void Clock_DrawButton(uint16_t x, uint16_t y, uint16_t width, uint16_t height,
                     const char* text, uint16_t bg_color, uint16_t text_color);
uint8_t Clock_CheckButtonPress(uint16_t touch_x, uint16_t touch_y);
void Clock_ProcessButtonPress(uint8_t button_id);

// Additional feature functions
void Clock_ShowSystemStatus(void);
void Clock_AddNewAlarm(void);
void Clock_ToggleAlarmEnabled(void);
void Clock_ResetToSystemTime(void);

#endif /* __CLOCK_DISPLAY_H */