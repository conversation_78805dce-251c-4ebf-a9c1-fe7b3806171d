# 嵌入式时钟系统 - 完整实现

## 项目概述

这是一个基于STM32F103的完整嵌入式时钟系统，实现了所有要求的功能，包括圆盘时钟显示、闹钟管理、串口通信等。

## 已实现的功能

### ✅ 1. 通用定时器产生毫秒定时
- **TIM2**: 1ms精确定时 (72MHz / (71+1) / (999+1) = 1000Hz)
- **TIM3**: 500ms定时，用于LED闪烁控制
- **TIM4**: 微秒级延时功能

### ✅ 2. 日期时间结构体设计
```c
typedef struct {
    uint16_t year;        // 年份 (2000-2099)
    uint8_t month;        // 月份 (1-12)
    uint8_t day;          // 日期 (1-31)
    uint8_t week;         // 星期 (0-6, 0为星期日)
    uint8_t hour;         // 小时 (0-23)
    uint8_t minute;       // 分钟 (0-59)
    uint8_t second;       // 秒钟 (0-59)
    uint16_t millisecond; // 毫秒 (0-999)
} DateTime;
```
- **时间计算**: 完全在中断外的`DateTime_Update()`函数中完成
- **闰年处理**: 自动处理闰年2月29日
- **星期计算**: 基于Zeller公式自动计算星期几

### ✅ 3. 闹钟管理系统
- **容量**: 支持最多10组闹钟
- **功能**: 添加、删除、修改、启用/禁用闹钟
- **重复设置**: 支持每日、工作日、周末、自定义重复模式
- **闹钟名称**: 每个闹钟可设置自定义名称

### ✅ 4. 闹钟触发提醒
- **串口提醒**: 闹钟触发时自动发送信息到PC串口助手
- **LED闪烁**: 闹钟触发时LED闪烁200次作为视觉提醒
- **精确触发**: 在每秒的0毫秒时检查闹钟，确保精确触发

### ✅ 5. 圆盘时钟显示 (新增亮点功能)
- **美观表盘**: 圆形表盘设计，带有12小时刻度和数字
- **三种指针**: 
  - 时针 (黑色粗线)
  - 分针 (蓝色中等线)
  - 秒针 (红色细线)
- **日期显示**: 表盘上方显示完整日期信息
- **数字时间**: 表盘下方显示数字时间作为补充

### ✅ 6. 串口通信协议
完整的串口命令系统，支持以下功能：

#### 基础命令
- `getTime` - 获取当前时间
- `setTime YYYY-MM-DD,HH:MM:SS` - 设置系统时间
- `help` - 显示命令帮助

#### 闹钟管理命令
- `listAlarms` - 列出所有闹钟
- `getAlarm INDEX` - 获取指定闹钟信息
- `setAlarm INDEX,HH:MM:SS,DAYS,NAME,ENABLED` - 设置闹钟
- `deleteAlarm INDEX` - 删除指定闹钟
- `enableAlarm INDEX` - 启用指定闹钟
- `disableAlarm INDEX` - 禁用指定闹钟

#### 系统状态命令
- `getStatus` - 获取系统状态信息

## 技术特点

### 1. 高精度时间管理
- 1ms精度的时间更新
- 中断中只进行简单计数，复杂计算在主循环中完成
- 自动处理闰年、月末、年末等边界情况

### 2. 优化的显示系统
- 圆盘时钟采用数学计算绘制指针
- 智能刷新机制，只更新变化的部分
- 支持多页面切换 (时钟、闹钟列表、设置等)

### 3. 完善的串口协议
- 命令解析容错性强
- 详细的错误提示和帮助信息
- 支持复杂的闹钟设置格式

### 4. 模块化设计
- 清晰的文件结构和函数分工
- 易于扩展和维护
- 良好的代码注释

## 使用示例

### 串口命令示例
```
# 设置系统时间
setTime 2023-12-31,23:59:50

# 添加工作日闹钟
setAlarm 0,07:00:00,0111110,Wake Up,1

# 添加每日闹钟
setAlarm 1,22:30:00,1111111,Bedtime,1

# 查看所有闹钟
listAlarms

# 获取系统状态
getStatus
```

## 文件结构

```
User/
├── main.c              # 主程序
├── rtc.c/h            # 时间和闹钟管理
├── clock_display.c/h   # 圆盘时钟显示
├── uart_protocol.c/h   # 串口通信协议
├── timer.c/h          # 定时器配置
├── usart.c/h          # 串口驱动
└── ...其他驱动文件

Hardware/
├── lcd.c/h            # LCD显示驱动
└── ...其他硬件驱动

System/
└── delay.c/h          # 延时函数
```

## 编译和运行

1. 使用Keil uVision打开`111.uvprojx`项目文件
2. 编译项目
3. 下载到STM32F103开发板
4. 连接串口助手 (波特率115200)
5. 享受完整的时钟系统功能！

## 系统要求

- **MCU**: STM32F103系列
- **时钟**: 72MHz系统时钟
- **显示**: LCD显示屏
- **串口**: USART1 (115200波特率)
- **按键**: 支持按键输入进行页面切换

---

*本项目完全满足所有设计要求，并在圆盘时钟显示方面有所创新，提供了美观实用的用户界面。*
