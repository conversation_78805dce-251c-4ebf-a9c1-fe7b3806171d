# 双协议支持说明

## 概述
系统现在同时支持**文本命令协议**和**数据帧协议**两种通信方式，完全兼容原有的串口助手操作方式。

## 🔄 **协议自动识别**
系统会自动识别接收到的数据格式：
- **文本命令**：以ASCII字符开头的命令（如 `setTime`、`getAlarm` 等）
- **数据帧**：以 `0xAA` 开头的二进制数据帧

## 📝 **文本命令协议（原有功能保持不变）**

### 时间设置命令
```
setTime 2024-01-15,14:30:25
```
**响应示例：**
```
SUCCESS: Time set to 2024-01-15 14:30:25 Weekday:1
```

### 获取时间命令
```
getTime
```
**响应示例：**
```
TIME=2024-01-15,14:30:25,W1
```

### 闹钟管理命令

#### 获取单个闹钟信息
```
getAlarm 0
```
**响应示例：**
```
ALARM0=07:00:00,Weekdays,Wake Up,1
```

#### 获取所有闹钟列表
```
listAlarms
```
**响应示例：**
```
ALARM_LIST: Total 2 alarms
ALARM0=07:00:00,Weekdays,Wake Up,1
ALARM1=22:30:00,Everyday,Bedtime,1
```

#### 设置闹钟
```
setAlarm 0,07:00:00,0111110,Wake Up,1
```
**参数说明：**
- `0`: 闹钟索引
- `07:00:00`: 闹钟时间
- `0111110`: 重复模式（7位二进制，周日到周六）
  - `1111111`: 每天
  - `0111110`: 工作日（周一到周五）
  - `1000001`: 周末（周六、周日）
- `Wake Up`: 闹钟名称
- `1`: 启用状态（1=启用，0=禁用）

**响应示例：**
```
SUCCESS: Alarm 0 updated
```

#### 其他闹钟命令
```
deleteAlarm 0     # 删除闹钟0
enableAlarm 0     # 启用闹钟0
disableAlarm 0    # 禁用闹钟0
```

### 系统状态和控制命令
```
getStatus         # 获取系统状态
help             # 显示帮助信息
startAutoTime    # 开始自动时间传输
stopAutoTime     # 停止自动时间传输
```

## 🔧 **数据帧协议（新增功能）**

### 数据帧格式
```
[帧头0xAA] [命令1字节] [长度1字节] [数据N字节] [校验1字节] [帧尾0x55]
```

### 示例命令

#### 获取时间
```
发送: AA 01 00 01 55
响应: AA 01 09 00 07 E8 01 0F 0E 1E 19 01 XX 55
     (2024年1月15日14:30:25 周一)
```

#### 设置时间
```
发送: AA 02 07 07 E8 01 0F 0E 1E 19 XX 55
     (设置为2024年1月15日14:30:25)
响应: AA 02 01 00 XX 55 (成功)
```

## 🎯 **使用建议**

### 串口助手设置
- **波特率**: 115200
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无
- **流控**: 无

### 测试步骤

1. **连接设备并打开串口**
2. **发送帮助命令**：
   ```
   help
   ```
3. **设置时间**：
   ```
   setTime 2024-01-15,14:30:25
   ```
4. **获取时间确认**：
   ```
   getTime
   ```
5. **设置工作日闹钟**：
   ```
   setAlarm 0,07:00:00,0111110,Wake Up,1
   ```
6. **查看闹钟列表**：
   ```
   listAlarms
   ```

## ✅ **兼容性保证**

### 完全向后兼容
- ✅ 所有原有文本命令格式保持不变
- ✅ 响应格式与原系统一致
- ✅ 参数格式和验证规则不变
- ✅ 错误提示信息保持原样

### 新增功能
- ✅ 支持高效的二进制数据帧通信
- ✅ 自动协议识别，无需手动切换
- ✅ 保持原有调试输出功能

## 🔍 **故障排除**

### 常见问题
1. **命令无响应**：检查串口连接和波特率设置
2. **格式错误**：确保命令格式正确，参数用逗号分隔
3. **时间设置失败**：检查时间参数范围是否有效
4. **闹钟设置失败**：确保重复模式为7位二进制字符串

### 调试方法
1. 发送 `help` 命令查看完整命令列表
2. 发送 `getStatus` 查看系统状态
3. 使用 `getTime` 确认时间设置是否成功

## 📊 **性能特点**

### 文本协议
- **优点**: 人类可读，易于调试，兼容性好
- **适用**: 手动测试，调试，串口助手操作

### 数据帧协议  
- **优点**: 传输效率高，解析速度快，可靠性强
- **适用**: 自动化系统，高频通信，嵌入式应用

系统会根据接收到的数据自动选择合适的协议进行处理，用户无需关心底层实现细节。
