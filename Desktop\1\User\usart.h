#ifndef __USART_H__
#define __USART_H__

/* Includes ------------------------------------------------------------------*/
#include "stm32f10x.h"
#include <stdio.h>

/* Exported types ------------------------------------------------------------*/
/* Exported constants --------------------------------------------------------*/
#define USART_REC_LEN  200  // 定义最大接收字节数
/* Exported macro ------------------------------------------------------------*/
/* Exported functions ------------------------------------------------------- */
void uart1_init(u32 bound);
void USART_SendString(USART_TypeDef *USARTx, char *str);

/* 接收缓冲区和状态变量 */
extern u8  USART_RX_BUF[USART_REC_LEN]; // 接收缓冲区
extern u16 USART_RX_STA;                // 接收状态标记

/* Standard library support --------------------------------------------------*/
#ifdef __GNUC__
  /* With GCC/RAISONANCE, small printf (option LD Linker->Libraries->Small printf
     set to 'Yes') calls __io_putchar() */
  #define PUTCHAR_PROTOTYPE int __io_putchar(int ch)
#else
  #define PUTCHAR_PROTOTYPE int fputc(int ch, FILE *f)
#endif /* __GNUC__ */
PUTCHAR_PROTOTYPE;
void _sys_exit(int x);

#endif /* __USART_H__ */ 
